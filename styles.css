* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 筛选器样式 */
.filter-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 作品网格样式 */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.portfolio-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.portfolio-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(30,60,114,0.8), rgba(42,82,152,0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.overlay-text {
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.portfolio-content {
    padding: 20px;
}

.portfolio-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.portfolio-category {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 10px;
}

.portfolio-description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0,0,0,0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255,255,255,0.9);
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: #000;
    background: rgba(255,255,255,1);
    transform: scale(1.1);
}

.modal-body {
    display: flex;
    flex-direction: row;
    min-height: 400px;
}

.modal-image {
    flex: 1;
    min-height: 400px;
    overflow: hidden;
    border-radius: 20px 0 0 20px;
}

.modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modal-info {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.modal-info h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 700;
}

.modal-category {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    width: fit-content;
}

.modal-description {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 30px;
}

.modal-details {
    margin-bottom: 30px;
}

.detail-item {
    margin-bottom: 15px;
    font-size: 14px;
}

.detail-item strong {
    color: #2c3e50;
    display: inline-block;
    width: 100px;
}

.modal-actions {
    display: flex;
    gap: 15px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .filter-container {
        gap: 10px;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 12px;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-body {
        flex-direction: column;
    }

    .modal-image {
        min-height: 250px;
        border-radius: 20px 20px 0 0;
    }

    .modal-info {
        padding: 30px 20px;
    }

    .modal-info h2 {
        font-size: 1.5rem;
    }

    .modal-actions {
        flex-direction: column;
    }

    .detail-item strong {
        width: 80px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .filter-container {
        flex-direction: column;
        align-items: center;
    }

    .modal-info {
        padding: 20px 15px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: white;
    font-size: 18px;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}