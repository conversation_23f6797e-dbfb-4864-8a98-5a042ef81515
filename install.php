<?php
/**
 * 安装脚本 - 自动创建数据库和表结构
 */

// 数据库配置
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'portfolio_db';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品集系统安装</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .config-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        .links a {
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <h1>🎨 作品集系统安装</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理安装请求
            $host = $_POST['host'] ?? 'localhost';
            $username = $_POST['username'] ?? 'root';
            $password = $_POST['password'] ?? '';
            $database = $_POST['database'] ?? 'portfolio_db';
            
            try {
                // 连接MySQL服务器
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo '<div class="step success">';
                echo '<h3>✅ 步骤 1: 数据库连接成功</h3>';
                echo '<p>成功连接到MySQL服务器</p>';
                echo '</div>';
                
                // 创建数据库
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$database`");
                
                echo '<div class="step success">';
                echo '<h3>✅ 步骤 2: 数据库创建成功</h3>';
                echo '<p>数据库 "' . $database . '" 已创建</p>';
                echo '</div>';
                
                // 读取并执行SQL文件
                $sql = file_get_contents('database.sql');
                $sql = str_replace('CREATE DATABASE IF NOT EXISTS portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
                $sql = str_replace('USE portfolio_db;', '', $sql);
                
                // 分割SQL语句并执行
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                echo '<div class="step success">';
                echo '<h3>✅ 步骤 3: 数据表创建成功</h3>';
                echo '<p>所有数据表和示例数据已创建</p>';
                echo '</div>';
                
                // 更新配置文件
                $configContent = "<?php
/**
 * 数据库配置文件
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    private \$conn;

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$e) {
            echo \"连接失败: \" . \$e->getMessage();
        }
        
        return \$this->conn;
    }
}

/**
 * 通用响应函数
 */
function sendResponse(\$data, \$status = 200, \$message = 'success') {
    http_response_code(\$status);
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    echo json_encode([
        'status' => \$status,
        'message' => \$message,
        'data' => \$data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 错误响应函数
 */
function sendError(\$message, \$status = 400) {
    sendResponse(null, \$status, \$message);
}

/**
 * 验证必填字段
 */
function validateRequired(\$data, \$required_fields) {
    \$missing = [];
    foreach (\$required_fields as \$field) {
        if (!isset(\$data[\$field]) || empty(trim(\$data[\$field]))) {
            \$missing[] = \$field;
        }
    }
    return \$missing;
}

/**
 * 清理输入数据
 */
function sanitizeInput(\$data) {
    return htmlspecialchars(strip_tags(trim(\$data)));
}

/**
 * 验证URL格式
 */
function isValidUrl(\$url) {
    return filter_var(\$url, FILTER_VALIDATE_URL) !== false;
}

/**
 * 验证分类
 */
function isValidCategory(\$category) {
    \$valid_categories = ['web', 'mobile', 'ui', 'graphic'];
    return in_array(\$category, \$valid_categories);
}

/**
 * 验证状态
 */
function isValidStatus(\$status) {
    \$valid_statuses = ['已完成', '开发中', '计划中', '暂停'];
    return in_array(\$status, \$valid_statuses);
}
?>";
                
                file_put_contents('config/database.php', $configContent);
                
                echo '<div class="step success">';
                echo '<h3>✅ 步骤 4: 配置文件更新成功</h3>';
                echo '<p>数据库配置已保存</p>';
                echo '</div>';
                
                echo '<div class="step success">';
                echo '<h3>🎉 安装完成！</h3>';
                echo '<p>作品集系统已成功安装，您现在可以开始使用了。</p>';
                echo '<p><strong>默认管理员账户：</strong></p>';
                echo '<p>用户名: admin<br>密码: admin123</p>';
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<div class="step error">';
                echo '<h3>❌ 安装失败</h3>';
                echo '<p>错误信息: ' . $e->getMessage() . '</p>';
                echo '</div>';
            }
        } else {
            // 显示安装表单
        ?>
        
        <div class="step">
            <h3>📋 安装说明</h3>
            <p>此安装程序将自动创建数据库、数据表并插入示例数据。请确保：</p>
            <ul>
                <li>MySQL服务已启动</li>
                <li>PHP已安装PDO扩展</li>
                <li>具有创建数据库的权限</li>
            </ul>
        </div>
        
        <form method="POST" class="config-form">
            <h3>数据库配置</h3>
            
            <div class="form-group">
                <label for="host">数据库主机:</label>
                <input type="text" id="host" name="host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="root" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="">
            </div>
            
            <div class="form-group">
                <label for="database">数据库名:</label>
                <input type="text" id="database" name="database" value="portfolio_db" required>
            </div>
            
            <button type="submit" class="btn">开始安装</button>
        </form>
        
        <?php } ?>
        
        <div class="links">
            <a href="index.html" class="btn">访问前台</a>
            <a href="admin/" class="btn">管理后台</a>
        </div>
    </div>
</body>
</html>
