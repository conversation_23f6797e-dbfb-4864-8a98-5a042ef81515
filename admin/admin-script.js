// 全局变量
let currentPage = 1;
let totalPages = 1;
let currentFilter = { category: 'all', status: 'all' };
let editingId = null;

// API基础URL
const API_BASE = '../api/portfolios.php';

// DOM元素
const portfoliosSection = document.getElementById('portfolios-section');
const addPortfolioSection = document.getElementById('add-portfolio-section');
const settingsSection = document.getElementById('settings-section');
const portfolioForm = document.getElementById('portfolio-form');
const portfoliosTbody = document.getElementById('portfolios-tbody');
const deleteModal = document.getElementById('delete-modal');
const loadingOverlay = document.getElementById('loading-overlay');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadPortfolios();
    checkDatabaseStatus();
});

// 设置事件监听器
function setupEventListeners() {
    // 侧边栏导航
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.dataset.section;
            switchSection(section);
            
            // 更新导航状态
            document.querySelectorAll('.sidebar-nav li').forEach(li => li.classList.remove('active'));
            this.parentElement.classList.add('active');
        });
    });
    
    // 添加新作品按钮
    document.getElementById('add-new-btn').addEventListener('click', function() {
        switchSection('add-portfolio');
        resetForm();
        document.getElementById('page-title').textContent = '添加新作品';
    });
    
    // 筛选器
    document.getElementById('category-filter').addEventListener('change', function() {
        currentFilter.category = this.value;
        currentPage = 1;
        loadPortfolios();
    });
    
    document.getElementById('status-filter').addEventListener('change', function() {
        currentFilter.status = this.value;
        currentPage = 1;
        loadPortfolios();
    });
    
    // 刷新按钮
    document.getElementById('refresh-btn').addEventListener('click', function() {
        loadPortfolios();
    });
    
    // 表单提交
    portfolioForm.addEventListener('submit', function(e) {
        e.preventDefault();
        savePortfolio();
    });
    
    // 取消按钮
    document.getElementById('cancel-btn').addEventListener('click', function() {
        switchSection('portfolios');
        resetForm();
    });
    
    // 删除确认模态框
    document.getElementById('cancel-delete').addEventListener('click', function() {
        deleteModal.style.display = 'none';
    });
    
    document.getElementById('confirm-delete').addEventListener('click', function() {
        const id = this.dataset.deleteId;
        if (id) {
            deletePortfolio(id);
        }
        deleteModal.style.display = 'none';
    });
    
    // 点击模态框外部关闭
    deleteModal.addEventListener('click', function(e) {
        if (e.target === deleteModal) {
            deleteModal.style.display = 'none';
        }
    });
}

// 切换页面区域
function switchSection(section) {
    document.querySelectorAll('.content-section').forEach(sec => sec.classList.remove('active'));
    
    switch(section) {
        case 'portfolios':
            portfoliosSection.classList.add('active');
            document.getElementById('page-title').textContent = '作品管理';
            break;
        case 'add-portfolio':
            addPortfolioSection.classList.add('active');
            document.getElementById('page-title').textContent = editingId ? '编辑作品' : '添加新作品';
            break;
        case 'settings':
            settingsSection.classList.add('active');
            document.getElementById('page-title').textContent = '系统设置';
            break;
    }
}

// 显示加载状态
function showLoading() {
    loadingOverlay.style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// 显示消息提示
function showMessage(message, type = 'success') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'success' ? '#28a745' : '#dc3545'};
        color: white;
        border-radius: 5px;
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageEl.remove();
    }, 3000);
}

// 加载作品列表
async function loadPortfolios() {
    try {
        showLoading();
        
        let url = `${API_BASE}?`;
        if (currentFilter.category !== 'all') {
            url += `category=${currentFilter.category}&`;
        }
        if (currentFilter.status !== 'all') {
            url += `status=${currentFilter.status}&`;
        }
        
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.status === 200) {
            renderPortfoliosTable(result.data);
        } else {
            showMessage('加载作品列表失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 渲染作品表格
function renderPortfoliosTable(portfolios) {
    portfoliosTbody.innerHTML = '';
    
    if (portfolios.length === 0) {
        portfoliosTbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px;">暂无数据</td></tr>';
        return;
    }
    
    portfolios.forEach(portfolio => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${portfolio.id}</td>
            <td><img src="${portfolio.image}" alt="${portfolio.title}" class="portfolio-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEwyNSAxNUwzNSAyNUw0MCAyMFY1SDE1VjIwWiIgZmlsbD0iI0NDQyIvPgo8L3N2Zz4K'"></td>
            <td>${portfolio.title}</td>
            <td><span class="category-badge category-${portfolio.category}">${getCategoryName(portfolio.category)}</span></td>
            <td><span class="status-badge status-${portfolio.status}">${portfolio.status}</span></td>
            <td>${formatDate(portfolio.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="editPortfolio(${portfolio.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="confirmDelete(${portfolio.id})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </td>
        `;
        portfoliosTbody.appendChild(row);
    });
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryNames = {
        'web': '网页设计',
        'mobile': '移动应用',
        'ui': 'UI设计',
        'graphic': '平面设计'
    };
    return categoryNames[category] || category;
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 编辑作品
async function editPortfolio(id) {
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE}?id=${id}`);
        const result = await response.json();
        
        if (result.status === 200) {
            const portfolio = result.data;
            fillForm(portfolio);
            editingId = id;
            switchSection('add-portfolio');
            document.getElementById('page-title').textContent = '编辑作品';
        } else {
            showMessage('加载作品详情失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 填充表单
function fillForm(portfolio) {
    document.getElementById('portfolio-id').value = portfolio.id;
    document.getElementById('title').value = portfolio.title;
    document.getElementById('category').value = portfolio.category;
    document.getElementById('image').value = portfolio.image;
    document.getElementById('status').value = portfolio.status;
    document.getElementById('description').value = portfolio.description;
    document.getElementById('full_description').value = portfolio.fullDescription;
    document.getElementById('tech').value = portfolio.tech;
    document.getElementById('project_date').value = portfolio.date;
    document.getElementById('project_link').value = portfolio.link || '';
    document.getElementById('github_link').value = portfolio.github || '';
    document.getElementById('sort_order').value = portfolio.sortOrder || 0;
    document.getElementById('is_featured').checked = portfolio.isFeatured || false;
}

// 重置表单
function resetForm() {
    portfolioForm.reset();
    editingId = null;
    document.getElementById('portfolio-id').value = '';
}

// 保存作品
async function savePortfolio() {
    try {
        showLoading();
        
        const formData = new FormData(portfolioForm);
        const data = {
            title: formData.get('title'),
            category: formData.get('category'),
            image: formData.get('image'),
            status: formData.get('status'),
            description: formData.get('description'),
            full_description: formData.get('full_description'),
            tech: formData.get('tech'),
            project_date: formData.get('project_date'),
            project_link: formData.get('project_link'),
            github_link: formData.get('github_link'),
            sort_order: parseInt(formData.get('sort_order')) || 0,
            is_featured: formData.get('is_featured') === 'on'
        };
        
        const url = editingId ? `${API_BASE}?id=${editingId}` : API_BASE;
        const method = editingId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.status === 200 || result.status === 201) {
            showMessage(editingId ? '作品更新成功' : '作品创建成功');
            switchSection('portfolios');
            resetForm();
            loadPortfolios();
        } else {
            showMessage('保存失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 确认删除
function confirmDelete(id) {
    document.getElementById('confirm-delete').dataset.deleteId = id;
    deleteModal.style.display = 'block';
}

// 删除作品
async function deletePortfolio(id) {
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE}?id=${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.status === 200) {
            showMessage('作品删除成功');
            loadPortfolios();
        } else {
            showMessage('删除失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 检查数据库状态
async function checkDatabaseStatus() {
    try {
        const response = await fetch(API_BASE);
        const result = await response.json();
        
        const statusEl = document.getElementById('db-status');
        if (result.status === 200) {
            statusEl.textContent = '正常';
            statusEl.style.color = '#28a745';
        } else {
            statusEl.textContent = '异常';
            statusEl.style.color = '#dc3545';
        }
    } catch (error) {
        const statusEl = document.getElementById('db-status');
        statusEl.textContent = '连接失败';
        statusEl.style.color = '#dc3545';
    }
}
