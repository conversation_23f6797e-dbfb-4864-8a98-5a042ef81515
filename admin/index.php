<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品集管理后台</title>
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-palette"></i> 作品集管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#" data-section="portfolios">
                            <i class="fas fa-images"></i> 作品管理
                        </a>
                    </li>
                    <li>
                        <a href="#" data-section="add-portfolio">
                            <i class="fas fa-plus"></i> 添加作品
                        </a>
                    </li>
                    <li>
                        <a href="#" data-section="settings">
                            <i class="fas fa-cog"></i> 系统设置
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <h1 id="page-title">作品管理</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" id="add-new-btn">
                        <i class="fas fa-plus"></i> 添加新作品
                    </button>
                </div>
            </header>

            <!-- 作品列表页面 -->
            <section id="portfolios-section" class="content-section active">
                <!-- 筛选器 -->
                <div class="filters">
                    <select id="category-filter">
                        <option value="all">所有分类</option>
                        <option value="web">网页设计</option>
                        <option value="mobile">移动应用</option>
                        <option value="ui">UI设计</option>
                        <option value="graphic">平面设计</option>
                    </select>
                    <select id="status-filter">
                        <option value="all">所有状态</option>
                        <option value="已完成">已完成</option>
                        <option value="开发中">开发中</option>
                        <option value="计划中">计划中</option>
                        <option value="暂停">暂停</option>
                    </select>
                    <button class="btn btn-secondary" id="refresh-btn">
                        <i class="fas fa-refresh"></i> 刷新
                    </button>
                </div>

                <!-- 作品表格 -->
                <div class="table-container">
                    <table class="portfolios-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>封面</th>
                                <th>标题</th>
                                <th>分类</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="portfolios-tbody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </div>
            </section>

            <!-- 添加/编辑作品页面 -->
            <section id="add-portfolio-section" class="content-section">
                <form id="portfolio-form" class="portfolio-form">
                    <input type="hidden" id="portfolio-id" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">作品标题 *</label>
                            <input type="text" id="title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="category">分类 *</label>
                            <select id="category" name="category" required>
                                <option value="">请选择分类</option>
                                <option value="web">网页设计</option>
                                <option value="mobile">移动应用</option>
                                <option value="ui">UI设计</option>
                                <option value="graphic">平面设计</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="image">封面图片URL *</label>
                            <input type="url" id="image" name="image" required>
                        </div>
                        <div class="form-group">
                            <label for="status">项目状态</label>
                            <select id="status" name="status">
                                <option value="已完成">已完成</option>
                                <option value="开发中">开发中</option>
                                <option value="计划中">计划中</option>
                                <option value="暂停">暂停</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">简短描述 *</label>
                        <textarea id="description" name="description" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="full_description">详细描述 *</label>
                        <textarea id="full_description" name="full_description" rows="5" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="tech">技术栈 *</label>
                            <input type="text" id="tech" name="tech" placeholder="例如：React, Node.js, MongoDB" required>
                        </div>
                        <div class="form-group">
                            <label for="project_date">完成时间 *</label>
                            <input type="text" id="project_date" name="project_date" placeholder="例如：2024年3月" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="project_link">项目链接</label>
                            <input type="url" id="project_link" name="project_link">
                        </div>
                        <div class="form-group">
                            <label for="github_link">GitHub链接</label>
                            <input type="url" id="github_link" name="github_link">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="sort_order">排序权重</label>
                            <input type="number" id="sort_order" name="sort_order" value="0" min="0">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="is_featured" name="is_featured">
                                <span class="checkmark"></span>
                                精选作品
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-btn">取消</button>
                        <button type="submit" class="btn btn-primary" id="save-btn">保存</button>
                    </div>
                </form>
            </section>

            <!-- 系统设置页面 -->
            <section id="settings-section" class="content-section">
                <div class="settings-content">
                    <h3>系统信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>PHP版本:</label>
                            <span><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="info-item">
                            <label>服务器时间:</label>
                            <span><?php echo date('Y-m-d H:i:s'); ?></span>
                        </div>
                        <div class="info-item">
                            <label>数据库状态:</label>
                            <span id="db-status">检查中...</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <h3>确认删除</h3>
            <p>确定要删除这个作品吗？此操作不可撤销。</p>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="cancel-delete">取消</button>
                <button class="btn btn-danger" id="confirm-delete">删除</button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <p>处理中...</p>
    </div>

    <script src="admin-script.js"></script>
</body>
</html>
