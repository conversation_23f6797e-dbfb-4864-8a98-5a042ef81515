// 作品数据
const portfolioData = [
    {
        id: 1,
        title: "电商网站设计",
        category: "web",
        image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop",
        description: "现代化的电商网站界面设计，注重用户体验和转化率优化。采用响应式设计，支持多设备访问。",
        fullDescription: "这是一个完整的电商网站设计项目，包含首页、商品列表、商品详情、购物车、结算等完整流程。设计风格简洁现代，色彩搭配和谐，用户体验流畅。项目采用了最新的设计趋势和用户界面最佳实践。",
        tech: "React, Node.js, MongoDB, Stripe",
        date: "2024年3月",
        status: "已完成",
        link: "https://example-ecommerce.com",
        github: "https://github.com/example/ecommerce"
    },
    {
        id: 2,
        title: "移动端音乐应用",
        category: "mobile",
        image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop",
        description: "优雅的音乐播放应用界面设计，支持在线播放和离线下载功能。",
        fullDescription: "这款音乐应用专注于提供优质的音乐体验，界面设计简洁优雅，操作流畅自然。支持歌词显示、播放列表管理、音质选择等功能。设计上采用了深色主题，减少眼部疲劳，同时突出音乐内容。",
        tech: "React Native, Redux, Firebase",
        date: "2024年2月",
        status: "开发中",
        link: "https://example-music.com",
        github: "https://github.com/example/music-app"
    },
    {
        id: 3,
        title: "企业品牌设计",
        category: "graphic",
        image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=300&fit=crop",
        description: "完整的企业视觉识别系统设计，包括Logo、名片、宣传册等。",
        fullDescription: "为一家科技创业公司设计的完整品牌视觉识别系统。包括Logo设计、企业色彩规范、字体规范、名片设计、信纸设计、宣传册设计等。设计风格现代简约，体现了公司的创新精神和专业形象。",
        tech: "Adobe Illustrator, Photoshop, InDesign",
        date: "2024年1月",
        status: "已完成",
        link: "https://example-brand.com",
        github: "#"
    },
    {
        id: 4,
        title: "仪表板UI设计",
        category: "ui",
        image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
        description: "数据可视化仪表板界面设计，清晰展示复杂数据信息。",
        fullDescription: "为企业级数据分析平台设计的仪表板界面。界面布局合理，信息层次清晰，数据可视化效果出色。支持多种图表类型，可自定义仪表板布局。设计上注重数据的可读性和用户操作的便利性。",
        tech: "Figma, Chart.js, D3.js",
        date: "2023年12月",
        status: "已完成",
        link: "https://example-dashboard.com",
        github: "https://github.com/example/dashboard"
    },
    {
        id: 5,
        title: "社交媒体应用",
        category: "mobile",
        image: "https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=300&fit=crop",
        description: "年轻人喜爱的社交媒体应用界面设计，注重互动体验。",
        fullDescription: "专为年轻用户群体设计的社交媒体应用。界面设计活泼有趣，交互动画流畅自然。支持图片、视频分享，实时聊天，动态发布等功能。设计上采用了鲜明的色彩和现代的设计元素。",
        tech: "Flutter, Firebase, WebRTC",
        date: "2023年11月",
        status: "已完成",
        link: "https://example-social.com",
        github: "https://github.com/example/social-app"
    },
    {
        id: 6,
        title: "作品集网站",
        category: "web",
        image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=400&h=300&fit=crop",
        description: "个人作品集展示网站，响应式设计，优雅展示创作作品。",
        fullDescription: "为设计师打造的个人作品集网站。网站设计简洁优雅，突出作品展示效果。采用响应式设计，在各种设备上都有良好的显示效果。包含作品展示、个人介绍、联系方式等模块。",
        tech: "Vue.js, Nuxt.js, SCSS",
        date: "2023年10月",
        status: "已完成",
        link: "https://example-portfolio.com",
        github: "https://github.com/example/portfolio"
    }
];

// DOM元素
const portfolioGrid = document.getElementById('portfolioGrid');
const filterBtns = document.querySelectorAll('.filter-btn');
const modal = document.getElementById('projectModal');
const closeModal = document.getElementById('closeModal');

// 当前筛选类别
let currentFilter = 'all';

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    renderPortfolio();
    setupEventListeners();
});

// 渲染作品集
function renderPortfolio(filter = 'all') {
    portfolioGrid.innerHTML = '<div class="loading">加载中...</div>';
    
    setTimeout(() => {
        const filteredData = filter === 'all' 
            ? portfolioData 
            : portfolioData.filter(item => item.category === filter);
        
        portfolioGrid.innerHTML = '';
        
        filteredData.forEach(item => {
            const portfolioItem = createPortfolioItem(item);
            portfolioGrid.appendChild(portfolioItem);
        });
        
        // 添加动画效果
        const items = portfolioGrid.querySelectorAll('.portfolio-item');
        items.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(30px)';
                item.style.transition = 'all 0.5s ease';
                
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    }, 300);
}

// 创建作品项目元素
function createPortfolioItem(item) {
    const div = document.createElement('div');
    div.className = 'portfolio-item';
    div.dataset.id = item.id;
    
    div.innerHTML = `
        <div class="portfolio-image">
            <img src="${item.image}" alt="${item.title}" loading="lazy">
            <div class="portfolio-overlay">
                <div class="overlay-text">查看详情</div>
            </div>
        </div>
        <div class="portfolio-content">
            <h3 class="portfolio-title">${item.title}</h3>
            <span class="portfolio-category">${getCategoryName(item.category)}</span>
            <p class="portfolio-description">${item.description}</p>
        </div>
    `;
    
    div.addEventListener('click', () => openModal(item));
    
    return div;
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryNames = {
        'web': '网页设计',
        'mobile': '移动应用',
        'ui': 'UI设计',
        'graphic': '平面设计'
    };
    return categoryNames[category] || category;
}

// 设置事件监听器
function setupEventListeners() {
    // 筛选按钮事件
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // 更新按钮状态
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 渲染筛选结果
            currentFilter = category;
            renderPortfolio(category);
        });
    });
    
    // 模态框关闭事件
    closeModal.addEventListener('click', closeModalHandler);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModalHandler();
        }
    });
}

// 打开模态框
function openModal(item) {
    document.getElementById('modalImage').src = item.image;
    document.getElementById('modalTitle').textContent = item.title;
    document.getElementById('modalCategory').textContent = getCategoryName(item.category);
    document.getElementById('modalDescription').textContent = item.fullDescription;
    document.getElementById('modalTech').textContent = item.tech;
    document.getElementById('modalDate').textContent = item.date;
    document.getElementById('modalStatus').textContent = item.status;
    document.getElementById('modalLink').href = item.link;
    document.getElementById('modalGithub').href = item.github;
    
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}
