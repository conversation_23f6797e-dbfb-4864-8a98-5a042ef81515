// API配置
const API_BASE = 'api/portfolios.php';

// 作品数据缓存
let portfolioData = [];

// DOM元素
const portfolioGrid = document.getElementById('portfolioGrid');
const filterBtns = document.querySelectorAll('.filter-btn');
const modal = document.getElementById('projectModal');
const closeModal = document.getElementById('closeModal');

// 当前筛选类别
let currentFilter = 'all';

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadPortfolioData();
});

// 从API加载作品数据
async function loadPortfolioData() {
    try {
        portfolioGrid.innerHTML = '<div class="loading">加载中...</div>';

        const response = await fetch(API_BASE);
        const result = await response.json();

        if (result.status === 200) {
            portfolioData = result.data;
            renderPortfolio();
        } else {
            portfolioGrid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            console.error('加载作品数据失败:', result.message);
        }
    } catch (error) {
        portfolioGrid.innerHTML = '<div class="loading">网络错误，请检查连接</div>';
        console.error('网络错误:', error);
    }
}

// 带筛选的数据加载
async function loadPortfolioDataWithFilter(category) {
    try {
        portfolioGrid.innerHTML = '<div class="loading">加载中...</div>';

        let url = API_BASE;
        if (category && category !== 'all') {
            url += `?category=${category}`;
        }

        const response = await fetch(url);
        const result = await response.json();

        if (result.status === 200) {
            portfolioData = result.data;
            renderPortfolio(category);
        } else {
            portfolioGrid.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
            console.error('加载作品数据失败:', result.message);
        }
    } catch (error) {
        portfolioGrid.innerHTML = '<div class="loading">网络错误，请检查连接</div>';
        console.error('网络错误:', error);
    }
}

// 渲染作品集
function renderPortfolio(filter = 'all') {
    // 如果是筛选模式，数据已经在API层面筛选过了
    const displayData = portfolioData;

    portfolioGrid.innerHTML = '';

    if (displayData.length === 0) {
        portfolioGrid.innerHTML = '<div class="loading">暂无作品数据</div>';
        return;
    }

    displayData.forEach(item => {
        const portfolioItem = createPortfolioItem(item);
        portfolioGrid.appendChild(portfolioItem);
    });

    // 添加动画效果
    const items = portfolioGrid.querySelectorAll('.portfolio-item');
    items.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            item.style.transition = 'all 0.5s ease';

            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 50);
        }, index * 100);
    });
}

// 创建作品项目元素
function createPortfolioItem(item) {
    const div = document.createElement('div');
    div.className = 'portfolio-item';
    div.dataset.id = item.id;
    
    div.innerHTML = `
        <div class="portfolio-image">
            <img src="${item.image}" alt="${item.title}" loading="lazy">
            <div class="portfolio-overlay">
                <div class="overlay-text">查看详情</div>
            </div>
        </div>
        <div class="portfolio-content">
            <h3 class="portfolio-title">${item.title}</h3>
            <span class="portfolio-category">${getCategoryName(item.category)}</span>
            <p class="portfolio-description">${item.description}</p>
        </div>
    `;
    
    div.addEventListener('click', () => openModal(item));
    
    return div;
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryNames = {
        'web': '网页设计',
        'mobile': '移动应用',
        'ui': 'UI设计',
        'graphic': '平面设计'
    };
    return categoryNames[category] || category;
}

// 设置事件监听器
function setupEventListeners() {
    // 筛选按钮事件
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.dataset.category;

            // 更新按钮状态
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 渲染筛选结果
            currentFilter = category;
            loadPortfolioDataWithFilter(category);
        });
    });
    
    // 模态框关闭事件
    closeModal.addEventListener('click', closeModalHandler);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModalHandler();
        }
    });
}

// 打开模态框
function openModal(item) {
    document.getElementById('modalImage').src = item.image;
    document.getElementById('modalTitle').textContent = item.title;
    document.getElementById('modalCategory').textContent = getCategoryName(item.category);
    document.getElementById('modalDescription').textContent = item.fullDescription;
    document.getElementById('modalTech').textContent = item.tech;
    document.getElementById('modalDate').textContent = item.date;
    document.getElementById('modalStatus').textContent = item.status;
    document.getElementById('modalLink').href = item.link;
    document.getElementById('modalGithub').href = item.github;
    
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}
