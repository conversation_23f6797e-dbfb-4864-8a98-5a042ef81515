<?php
/**
 * 数据库配置文件
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'portfolio_db';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $e) {
            echo "连接失败: " . $e->getMessage();
        }
        
        return $this->conn;
    }
}

/**
 * 通用响应函数
 */
function sendResponse($data, $status = 200, $message = 'success') {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    echo json_encode([
        'status' => $status,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 错误响应函数
 */
function sendError($message, $status = 400) {
    sendResponse(null, $status, $message);
}

/**
 * 验证必填字段
 */
function validateRequired($data, $required_fields) {
    $missing = [];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * 清理输入数据
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * 验证URL格式
 */
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * 验证分类
 */
function isValidCategory($category) {
    $valid_categories = ['web', 'mobile', 'ui', 'graphic'];
    return in_array($category, $valid_categories);
}

/**
 * 验证状态
 */
function isValidStatus($status) {
    $valid_statuses = ['已完成', '开发中', '计划中', '暂停'];
    return in_array($status, $valid_statuses);
}
?>
