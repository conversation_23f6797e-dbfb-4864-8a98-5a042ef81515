<?php
/**
 * 作品集API接口
 * 支持GET, POST, PUT, DELETE操作
 */

require_once '../config/database.php';

// 处理OPTIONS请求（CORS预检）
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    exit(0);
}

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

// 获取ID参数（如果存在）
$id = isset($_GET['id']) ? intval($_GET['id']) : null;

switch ($method) {
    case 'GET':
        if ($id) {
            getPortfolio($db, $id);
        } else {
            getPortfolios($db);
        }
        break;
    
    case 'POST':
        createPortfolio($db);
        break;
    
    case 'PUT':
        if ($id) {
            updatePortfolio($db, $id);
        } else {
            sendError('缺少作品ID参数');
        }
        break;
    
    case 'DELETE':
        if ($id) {
            deletePortfolio($db, $id);
        } else {
            sendError('缺少作品ID参数');
        }
        break;
    
    default:
        sendError('不支持的请求方法', 405);
        break;
}

/**
 * 获取所有作品
 */
function getPortfolios($db) {
    try {
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $featured = isset($_GET['featured']) ? $_GET['featured'] : null;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
        
        $sql = "SELECT * FROM portfolios WHERE 1=1";
        $params = [];
        
        // 分类筛选
        if ($category && $category !== 'all' && isValidCategory($category)) {
            $sql .= " AND category = :category";
            $params[':category'] = $category;
        }
        
        // 精选筛选
        if ($featured !== null) {
            $sql .= " AND is_featured = :featured";
            $params[':featured'] = $featured === 'true' ? 1 : 0;
        }
        
        // 排序
        $sql .= " ORDER BY sort_order ASC, created_at DESC";
        
        // 分页
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
        }
        
        $stmt = $db->prepare($sql);
        
        // 绑定参数
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        
        $stmt->execute();
        $portfolios = $stmt->fetchAll();
        
        // 转换数据格式以匹配前端
        $formatted_portfolios = array_map(function($portfolio) {
            return [
                'id' => intval($portfolio['id']),
                'title' => $portfolio['title'],
                'category' => $portfolio['category'],
                'image' => $portfolio['image'],
                'description' => $portfolio['description'],
                'fullDescription' => $portfolio['full_description'],
                'tech' => $portfolio['tech'],
                'date' => $portfolio['project_date'],
                'status' => $portfolio['status'],
                'link' => $portfolio['project_link'],
                'github' => $portfolio['github_link'],
                'sortOrder' => intval($portfolio['sort_order']),
                'isFeatured' => (bool)$portfolio['is_featured'],
                'createdAt' => $portfolio['created_at'],
                'updatedAt' => $portfolio['updated_at']
            ];
        }, $portfolios);
        
        sendResponse($formatted_portfolios);
        
    } catch (Exception $e) {
        sendError('获取作品列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取单个作品
 */
function getPortfolio($db, $id) {
    try {
        $sql = "SELECT * FROM portfolios WHERE id = :id";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $portfolio = $stmt->fetch();
        
        if (!$portfolio) {
            sendError('作品不存在', 404);
        }
        
        // 格式化数据
        $formatted_portfolio = [
            'id' => intval($portfolio['id']),
            'title' => $portfolio['title'],
            'category' => $portfolio['category'],
            'image' => $portfolio['image'],
            'description' => $portfolio['description'],
            'fullDescription' => $portfolio['full_description'],
            'tech' => $portfolio['tech'],
            'date' => $portfolio['project_date'],
            'status' => $portfolio['status'],
            'link' => $portfolio['project_link'],
            'github' => $portfolio['github_link'],
            'sortOrder' => intval($portfolio['sort_order']),
            'isFeatured' => (bool)$portfolio['is_featured'],
            'createdAt' => $portfolio['created_at'],
            'updatedAt' => $portfolio['updated_at']
        ];
        
        sendResponse($formatted_portfolio);
        
    } catch (Exception $e) {
        sendError('获取作品详情失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 创建新作品
 */
function createPortfolio($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendError('无效的JSON数据');
        }
        
        // 验证必填字段
        $required_fields = ['title', 'category', 'image', 'description', 'full_description', 'tech', 'project_date'];
        $missing_fields = validateRequired($input, $required_fields);
        
        if (!empty($missing_fields)) {
            sendError('缺少必填字段: ' . implode(', ', $missing_fields));
        }
        
        // 验证数据格式
        if (!isValidCategory($input['category'])) {
            sendError('无效的分类');
        }
        
        if (!isValidUrl($input['image'])) {
            sendError('无效的图片URL');
        }
        
        if (isset($input['status']) && !isValidStatus($input['status'])) {
            sendError('无效的项目状态');
        }
        
        // 清理输入数据
        $data = [
            'title' => sanitizeInput($input['title']),
            'category' => $input['category'],
            'image' => $input['image'],
            'description' => sanitizeInput($input['description']),
            'full_description' => sanitizeInput($input['full_description']),
            'tech' => sanitizeInput($input['tech']),
            'project_date' => sanitizeInput($input['project_date']),
            'status' => isset($input['status']) ? $input['status'] : '已完成',
            'project_link' => isset($input['project_link']) ? $input['project_link'] : null,
            'github_link' => isset($input['github_link']) ? $input['github_link'] : null,
            'sort_order' => isset($input['sort_order']) ? intval($input['sort_order']) : 0,
            'is_featured' => isset($input['is_featured']) ? (bool)$input['is_featured'] : false
        ];
        
        $sql = "INSERT INTO portfolios (title, category, image, description, full_description, tech, project_date, status, project_link, github_link, sort_order, is_featured) 
                VALUES (:title, :category, :image, :description, :full_description, :tech, :project_date, :status, :project_link, :github_link, :sort_order, :is_featured)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($data);
        
        $new_id = $db->lastInsertId();
        
        sendResponse(['id' => $new_id], 201, '作品创建成功');
        
    } catch (Exception $e) {
        sendError('创建作品失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新作品
 */
function updatePortfolio($db, $id) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            sendError('无效的JSON数据');
        }

        // 检查作品是否存在
        $check_sql = "SELECT id FROM portfolios WHERE id = :id";
        $check_stmt = $db->prepare($check_sql);
        $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $check_stmt->execute();

        if (!$check_stmt->fetch()) {
            sendError('作品不存在', 404);
        }

        // 构建更新字段
        $update_fields = [];
        $params = [':id' => $id];

        $allowed_fields = [
            'title', 'category', 'image', 'description', 'full_description',
            'tech', 'project_date', 'status', 'project_link', 'github_link',
            'sort_order', 'is_featured'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($input[$field])) {
                $db_field = $field;
                if ($field === 'full_description') $db_field = 'full_description';
                if ($field === 'project_date') $db_field = 'project_date';
                if ($field === 'project_link') $db_field = 'project_link';
                if ($field === 'github_link') $db_field = 'github_link';
                if ($field === 'sort_order') $db_field = 'sort_order';
                if ($field === 'is_featured') $db_field = 'is_featured';

                $update_fields[] = "$db_field = :$field";

                // 数据验证和清理
                if ($field === 'category' && !isValidCategory($input[$field])) {
                    sendError('无效的分类');
                }
                if ($field === 'status' && !isValidStatus($input[$field])) {
                    sendError('无效的项目状态');
                }
                if ($field === 'image' && !isValidUrl($input[$field])) {
                    sendError('无效的图片URL');
                }

                if (in_array($field, ['title', 'description', 'full_description', 'tech', 'project_date'])) {
                    $params[":$field"] = sanitizeInput($input[$field]);
                } elseif ($field === 'is_featured') {
                    $params[":$field"] = (bool)$input[$field];
                } elseif ($field === 'sort_order') {
                    $params[":$field"] = intval($input[$field]);
                } else {
                    $params[":$field"] = $input[$field];
                }
            }
        }

        if (empty($update_fields)) {
            sendError('没有要更新的字段');
        }

        $sql = "UPDATE portfolios SET " . implode(', ', $update_fields) . " WHERE id = :id";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);

        sendResponse(null, 200, '作品更新成功');

    } catch (Exception $e) {
        sendError('更新作品失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除作品
 */
function deletePortfolio($db, $id) {
    try {
        // 检查作品是否存在
        $check_sql = "SELECT id FROM portfolios WHERE id = :id";
        $check_stmt = $db->prepare($check_sql);
        $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $check_stmt->execute();

        if (!$check_stmt->fetch()) {
            sendError('作品不存在', 404);
        }

        // 删除作品
        $sql = "DELETE FROM portfolios WHERE id = :id";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        sendResponse(null, 200, '作品删除成功');

    } catch (Exception $e) {
        sendError('删除作品失败: ' . $e->getMessage(), 500);
    }
}
?>
