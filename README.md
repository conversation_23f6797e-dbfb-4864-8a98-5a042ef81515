# 作品集海报墙 + PHP后端管理系统

一个现代化的作品集展示页面，采用影视墙风格设计，支持分类浏览和详情弹窗展示。包含完整的PHP后端管理系统，支持作品数据的增删改查操作。

## 🌟 功能特点

### 前端展示
- **🎨 影视墙风格设计** - 网格布局展示作品，悬停效果和动画过渡
- **🔍 分类筛选功能** - 支持按分类筛选：网页设计、移动应用、UI设计、平面设计
- **📱 弹窗详情展示** - 点击作品卡片弹出详情，显示完整项目信息
- **📱 响应式设计** - 完美适配桌面、平板和手机设备

### 后端管理
- **🛠 作品管理** - 完整的增删改查功能
- **📊 数据统计** - 作品数量、分类统计
- **🎯 分类筛选** - 按分类和状态筛选作品
- **⚡ RESTful API** - 标准的API接口设计
- **🔒 数据验证** - 完整的输入验证和安全防护

## 📁 文件结构

```
├── index.html              # 前端主页面
├── styles.css              # 前端样式文件
├── script.js               # 前端JavaScript逻辑
├── install.php             # 一键安装脚本
├── database.sql            # 数据库结构文件
├── config/
│   └── database.php        # 数据库配置文件
├── api/
│   └── portfolios.php      # 作品API接口
├── admin/
│   ├── index.php           # 管理后台主页
│   ├── admin-styles.css    # 后台样式文件
│   └── admin-script.js     # 后台JavaScript逻辑
├── images/                 # 图片资源文件夹
└── README.md               # 说明文档
```

## 🚀 快速开始

### 方法一：一键安装（推荐）

1. **环境要求**
   - PHP 7.4+
   - MySQL 5.7+
   - Web服务器（Apache/Nginx）或PHP内置服务器

2. **安装步骤**
   ```bash
   # 1. 下载项目文件到Web目录
   # 2. 启动PHP服务器
   php -S localhost:8000

   # 3. 访问安装页面
   http://localhost:8000/install.php
   ```

3. **配置数据库**
   - 在安装页面填写数据库信息
   - 点击"开始安装"自动创建数据库和表结构
   - 安装完成后即可使用

### 方法二：手动安装

1. **创建数据库**
   ```sql
   # 导入数据库结构
   mysql -u root -p < database.sql
   ```

2. **配置数据库连接**
   编辑 `config/database.php` 文件，修改数据库连接信息：
   ```php
   private $host = 'localhost';
   private $db_name = 'portfolio_db';
   private $username = 'root';
   private $password = 'your_password';
   ```

3. **启动服务**
   ```bash
   php -S localhost:8000
   ```

### 访问地址

- **前端展示页面**: `http://localhost:8000/index.html`
- **管理后台**: `http://localhost:8000/admin/`
- **API接口**: `http://localhost:8000/api/portfolios.php`

### 默认管理员账户
- 用户名: `admin`
- 密码: `admin123`

## 🔧 API接口文档

### 获取作品列表
```http
GET /api/portfolios.php
GET /api/portfolios.php?category=web
GET /api/portfolios.php?featured=true
```

### 获取单个作品
```http
GET /api/portfolios.php?id=1
```

### 创建作品
```http
POST /api/portfolios.php
Content-Type: application/json

{
    "title": "项目标题",
    "category": "web",
    "image": "图片URL",
    "description": "简短描述",
    "full_description": "详细描述",
    "tech": "技术栈",
    "project_date": "2024年3月",
    "status": "已完成",
    "project_link": "项目链接",
    "github_link": "GitHub链接",
    "sort_order": 0,
    "is_featured": false
}
```

### 更新作品
```http
PUT /api/portfolios.php?id=1
Content-Type: application/json

{
    "title": "更新的标题",
    "status": "开发中"
}
```

### 删除作品
```http
DELETE /api/portfolios.php?id=1
```

## 💻 技术栈

### 前端
- **HTML5** - 页面结构
- **CSS3** - 样式和动画（Grid布局、Flexbox、渐变、动画）
- **JavaScript ES6+** - 交互逻辑（Fetch API、异步处理）
- **响应式设计** - 多设备支持

### 后端
- **PHP 7.4+** - 服务端逻辑
- **MySQL** - 数据存储
- **PDO** - 数据库操作
- **RESTful API** - 接口设计

## 🌐 浏览器支持

- Chrome 60+ (推荐)
- Firefox 55+
- Safari 12+
- Edge 79+

## 🎨 自定义指南

### 修改颜色主题
在 `styles.css` 中修改以下变量：
```css
/* 主背景渐变 */
background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);

/* 按钮颜色 */
.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
}
```

### 添加新的分类
1. 在数据库中修改 `category` 字段的枚举值
2. 在前端HTML中添加新的筛选按钮
3. 在JavaScript的 `getCategoryName` 函数中添加对应的中文名称
4. 在后端API的验证函数中添加新分类

### 自定义字段
1. 在数据库表中添加新字段
2. 修改API接口的验证和处理逻辑
3. 更新管理后台的表单和显示
4. 调整前端的数据展示

## 🔒 安全建议

1. **修改默认管理员密码**
2. **配置HTTPS**（生产环境）
3. **设置文件权限**（config目录只读）
4. **启用SQL防注入保护**（已内置PDO预处理）
5. **配置CORS策略**（根据需要调整）

## 📝 许可证

MIT License - 可自由使用和修改

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 贡献指南
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看本文档的常见问题部分
2. 检查浏览器控制台的错误信息
3. 确认PHP和MySQL版本兼容性
4. 提交Issue描述具体问题
