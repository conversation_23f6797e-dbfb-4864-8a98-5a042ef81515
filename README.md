# 作品集海报墙

一个现代化的作品集展示页面，采用影视墙风格设计，支持分类浏览和详情弹窗展示。

## 功能特点

### 🎨 影视墙风格设计
- 网格布局展示作品
- 悬停效果和动画过渡
- 现代化的视觉设计
- 响应式布局支持

### 🔍 分类筛选功能
- 全部作品展示
- 网页设计分类
- 移动应用分类
- UI设计分类
- 平面设计分类

### 📱 弹窗详情展示
- 点击作品卡片弹出详情
- 显示项目完整信息
- 技术栈、完成时间、项目状态
- 访问项目和查看代码按钮

### 📱 响应式设计
- 支持桌面端、平板和手机
- 自适应网格布局
- 移动端优化的交互体验

## 文件结构

```
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript交互逻辑
├── images/             # 图片资源文件夹
└── README.md           # 说明文档
```

## 使用方法

### 1. 启动本地服务器
```bash
# 使用Python启动服务器
python3 -m http.server 8000

# 或使用Node.js
npx serve .

# 或使用PHP
php -S localhost:8000
```

### 2. 访问页面
在浏览器中打开 `http://localhost:8000`

### 3. 自定义作品数据
编辑 `script.js` 文件中的 `portfolioData` 数组，添加你的作品信息：

```javascript
{
    id: 1,
    title: "项目标题",
    category: "web", // web, mobile, ui, graphic
    image: "图片URL",
    description: "简短描述",
    fullDescription: "详细描述",
    tech: "技术栈",
    date: "完成时间",
    status: "项目状态",
    link: "项目链接",
    github: "GitHub链接"
}
```

## 技术栈

- **HTML5** - 页面结构
- **CSS3** - 样式和动画
- **JavaScript** - 交互逻辑
- **响应式设计** - 多设备支持

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 自定义指南

### 修改颜色主题
在 `styles.css` 中修改以下变量：
- 主背景渐变：`background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);`
- 按钮颜色：`.filter-btn` 和 `.btn-primary` 样式
- 卡片阴影：`.portfolio-item` 的 `box-shadow`

### 添加新的分类
1. 在HTML中添加新的筛选按钮
2. 在JavaScript的 `getCategoryName` 函数中添加对应的中文名称
3. 在作品数据中使用新的分类标识

### 修改布局
- 调整 `.portfolio-grid` 的 `grid-template-columns` 属性
- 修改 `.portfolio-item` 的尺寸和间距
- 自定义响应式断点

## 许可证

MIT License - 可自由使用和修改

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
