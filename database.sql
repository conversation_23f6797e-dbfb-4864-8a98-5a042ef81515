-- 创建数据库
CREATE DATABASE IF NOT EXISTS portfolio_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE portfolio_db;

-- 创建作品表
CREATE TABLE IF NOT EXISTS portfolios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '作品标题',
    category ENUM('web', 'mobile', 'ui', 'graphic') NOT NULL COMMENT '作品分类',
    image VARCHAR(500) NOT NULL COMMENT '封面图片URL',
    description TEXT NOT NULL COMMENT '简短描述',
    full_description TEXT NOT NULL COMMENT '详细描述',
    tech VARCHAR(500) NOT NULL COMMENT '技术栈',
    project_date VARCHAR(50) NOT NULL COMMENT '完成时间',
    status ENUM('已完成', '开发中', '计划中', '暂停') DEFAULT '已完成' COMMENT '项目状态',
    project_link VARCHAR(500) COMMENT '项目链接',
    github_link VARCHAR(500) COMMENT 'GitHub链接',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否为精选作品',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 插入示例数据
INSERT INTO portfolios (title, category, image, description, full_description, tech, project_date, status, project_link, github_link, sort_order, is_featured) VALUES
('电商网站设计', 'web', 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop', '现代化的电商网站界面设计，注重用户体验和转化率优化。采用响应式设计，支持多设备访问。', '这是一个完整的电商网站设计项目，包含首页、商品列表、商品详情、购物车、结算等完整流程。设计风格简洁现代，色彩搭配和谐，用户体验流畅。项目采用了最新的设计趋势和用户界面最佳实践。', 'React, Node.js, MongoDB, Stripe', '2024年3月', '已完成', 'https://example-ecommerce.com', 'https://github.com/example/ecommerce', 1, TRUE),

('移动端音乐应用', 'mobile', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop', '优雅的音乐播放应用界面设计，支持在线播放和离线下载功能。', '这款音乐应用专注于提供优质的音乐体验，界面设计简洁优雅，操作流畅自然。支持歌词显示、播放列表管理、音质选择等功能。设计上采用了深色主题，减少眼部疲劳，同时突出音乐内容。', 'React Native, Redux, Firebase', '2024年2月', '开发中', 'https://example-music.com', 'https://github.com/example/music-app', 2, TRUE),

('企业品牌设计', 'graphic', 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=300&fit=crop', '完整的企业视觉识别系统设计，包括Logo、名片、宣传册等。', '为一家科技创业公司设计的完整品牌视觉识别系统。包括Logo设计、企业色彩规范、字体规范、名片设计、信纸设计、宣传册设计等。设计风格现代简约，体现了公司的创新精神和专业形象。', 'Adobe Illustrator, Photoshop, InDesign', '2024年1月', '已完成', 'https://example-brand.com', '#', 3, FALSE),

('仪表板UI设计', 'ui', 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop', '数据可视化仪表板界面设计，清晰展示复杂数据信息。', '为企业级数据分析平台设计的仪表板界面。界面布局合理，信息层次清晰，数据可视化效果出色。支持多种图表类型，可自定义仪表板布局。设计上注重数据的可读性和用户操作的便利性。', 'Figma, Chart.js, D3.js', '2023年12月', '已完成', 'https://example-dashboard.com', 'https://github.com/example/dashboard', 4, TRUE),

('社交媒体应用', 'mobile', 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=300&fit=crop', '年轻人喜爱的社交媒体应用界面设计，注重互动体验。', '专为年轻用户群体设计的社交媒体应用。界面设计活泼有趣，交互动画流畅自然。支持图片、视频分享，实时聊天，动态发布等功能。设计上采用了鲜明的色彩和现代的设计元素。', 'Flutter, Firebase, WebRTC', '2023年11月', '已完成', 'https://example-social.com', 'https://github.com/example/social-app', 5, FALSE),

('作品集网站', 'web', 'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=400&h=300&fit=crop', '个人作品集展示网站，响应式设计，优雅展示创作作品。', '为设计师打造的个人作品集网站。网站设计简洁优雅，突出作品展示效果。采用响应式设计，在各种设备上都有良好的显示效果。包含作品展示、个人介绍、联系方式等模块。', 'Vue.js, Nuxt.js, SCSS', '2023年10月', '已完成', 'https://example-portfolio.com', 'https://github.com/example/portfolio', 6, FALSE);

-- 创建管理员表（可选，用于后台登录）
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    email VARCHAR(100) COMMENT '邮箱',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 插入默认管理员账户（用户名：admin，密码：admin123）
INSERT INTO admin_users (username, password, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- 创建索引优化查询性能
CREATE INDEX idx_category ON portfolios(category);
CREATE INDEX idx_status ON portfolios(status);
CREATE INDEX idx_featured ON portfolios(is_featured);
CREATE INDEX idx_sort_order ON portfolios(sort_order);
CREATE INDEX idx_created_at ON portfolios(created_at);
